# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a BCMI (Brain-Computer and Machine Intelligence) lab website built with **Payload CMS 3.48.0** and **Next.js 15.3.2** using the App Router. The project rebuilds the existing lab website (https://bcmi.sjtu.edu.cn/) with modern technology stack including Payload CMS for content management and shadcn/ui for enhanced UI/UX.

## Technology Stack

- **Framework**: Next.js 15.3.2 with App Router
- **CMS**: Payload CMS 3.48.0 (headless CMS)
- **Database**: PostgreSQL (configured via `DATABASE_URI` env variable)
- **Language**: TypeScript 5.7.3
- **Package Manager**: pnpm
- **Testing**: Playwright (E2E) + Vitest (unit/integration)
- **Styling**: CSS + planned shadcn/ui integration
- **Image Processing**: Sharp 0.34.2

## Development Commands

```bash
# Development server
pnpm dev

# Production build (with memory optimization)
pnpm build

# Run all tests (integration + E2E)
pnpm test

# Generate TypeScript types from Payload schema
pnpm generate:types

# Linting
pnpm lint
```

## Architecture

### Directory Structure
- `src/app/(frontend)/` - Public website pages using Next.js App Router
- `src/app/(payload)/` - Payload CMS admin interface and API routes
- `src/collections/` - Payload collections (Users, Media, and future lab content)
- `src/payload.config.ts` - Main Payload configuration

### Key Configuration Files
- `payload.config.ts` - Database, collections, admin settings
- `next.config.mjs` - Next.js with Payload integration using `withPayload` wrapper
- `tsconfig.json` - Path aliases: `@/*` for src, `@payload-config` for config

### Authentication & Admin
- Admin access via `/admin` route
- User authentication using Users collection with email/password
- Admin interface automatically generated by Payload

### Content Management
- Collections defined in `src/collections/` are auto-reflected in admin UI
- Media collection handles file uploads with Sharp image processing
- GraphQL and REST APIs auto-generated from collections

## Database Setup

Configure `.env` file with:
```
DATABASE_URI=postgresql://user:password@localhost:5432/bcmi
PAYLOAD_SECRET=your-secret-key
```

Alternative MongoDB setup available via `docker-compose up` (currently configured but not used).

## Type Safety

- Payload auto-generates TypeScript types from collections
- Run `pnpm generate:types` after modifying collections
- Import types from `@/payload-types`

## Lab-Specific Development Notes

The website should showcase:
- Research areas: Brain Computer Interface, Computer Vision, Speech Processing, NLP, ML
- Team members (professors and students)
- Publications and projects
- News and events
- Bilingual support (English/Chinese)

Collections needed:
- Researchers (with roles, bios, photos)
- Projects (with descriptions, images, status)
- Publications (with citations, PDFs)
- News/Events (with dates, content)
- Research Areas (with descriptions, related projects)