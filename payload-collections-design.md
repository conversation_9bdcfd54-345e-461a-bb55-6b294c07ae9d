# Payload CMS Collections Design for BCMI Lab Website

Based on the page structure and functional requirements in PRD.md, here are the comprehensive collection designs:

## 1. Homepage Settings (Global)
**Purpose**: Manage homepage content and settings

```typescript
{
  // Lab Introduction Banner
  labIntroTitle: string // Lab introduction title
  labIntroSubtitle: string // Lab introduction subtitle  
  labIntroDescription: richText // Lab description content
  labIntroBackgroundImage: upload // Banner background image
  labIntroVideo?: upload // Optional intro video (marked as optional - may not be immediately needed)
  
  // News Carousel
  featuredNews: relationship<News[]> // Featured news for carousel (max 5-6 items)
  
  // Research Areas Quick Navigation
  featuredResearchAreas: relationship<ResearchArea[]> // Featured research areas for homepage
  
  // Featured Projects (Optional)
  featuredProjects: relationship<Project[]> // Featured projects for homepage display
  featuredProjectsEnabled: checkbox // Toggle to show/hide featured projects section
  
  // Contact Information (Optional)
  contactEnabled: checkbox // Toggle to show/hide contact section
  contactTitle: string // Contact section title
  contactAddress: string // Lab address
  contactPhone: string // Contact phone
  contactEmail: string // Contact email
  contactMapEmbed?: string // Optional Google Maps embed code
  
  // SEO and Meta
  siteTitle: string // Site title for SEO
  siteDescription: string // Site description for SEO
  siteLogo: upload // Site logo
  siteFavicon: upload // Site favicon
}
```

## 2. Research Areas Collection
**Purpose**: Manage research domains and their content

```typescript
{
  // Basic Information
  name: string // Research area name (e.g., "Brain Computer Interface")
  slug: string // URL slug for the research area
  description: richText // Detailed description of the research area
  shortDescription: string // Brief description for cards/previews
  
  // Visual Elements
  icon: upload // Research area icon/image
  coverImage: upload // Cover image for detail page
  color?: string // Optional theme color for the research area
  
  // Content Organization
  order: number // Display order on research page
  isActive: checkbox // Whether to display this research area
  isFeatured: checkbox // Whether to feature on homepage
  
  // Relationships
  relatedProjects: relationship<Project[]> // Projects in this research area
  relatedPublications: relationship<Publication[]> // Publications in this area
  relatedMembers: relationship<Member[]> // Members working in this area
  
  // SEO
  metaTitle?: string // Optional custom meta title
  metaDescription?: string // Optional custom meta description
  
  // Future expansion fields (marked as optional)
  researchGoals?: richText // Optional detailed research goals
  keyTechnologies?: string[] // Optional key technologies used
  collaborations?: string[] // Optional external collaborations
}
```

## 3. Members Collection
**Purpose**: Manage team members information

```typescript
{
  // Basic Information
  name: string // Full name
  email: string // Email address
  phone?: string // Optional phone number
  
  // Professional Information
  role: select<'faculty' | 'postdoc' | 'phd' | 'master' | 'undergraduate' | 'alumni'> // Member category
  title?: string // Optional professional title (e.g., "Professor", "Associate Professor")
  position: string // Current position/role description
  
  // Visual and Content
  photo: upload // Member photo
  bio: richText // Biography and research interests
  shortBio?: string // Optional brief bio for cards
  
  // Academic Information
  researchInterests: string[] // Array of research interests
  education?: richText // Optional educational background
  experience?: richText // Optional work experience
  
  // External Links
  homepage?: string // Optional personal homepage
  googleScholar?: string // Optional Google Scholar profile
  orcid?: string // Optional ORCID ID
  linkedin?: string // Optional LinkedIn profile
  github?: string // Optional GitHub profile
  
  // Dates and Status
  joinDate: date // Date joined the lab
  graduationDate?: date // Optional graduation date (for students/alumni)
  isActive: checkbox // Whether currently active member
  
  // Display Settings
  displayOrder: number // Order for displaying members
  showOnWebsite: checkbox // Whether to show on public website
  
  // Relationships
  researchAreas: relationship<ResearchArea[]> // Research areas of interest
  projects: relationship<Project[]> // Projects involved in
  publications: relationship<Publication[]> // Publications authored
  
  // Future expansion (marked as optional)
  awards?: richText // Optional awards and honors
  teachingCourses?: string[] // Optional courses taught (for faculty)
  supervisionInfo?: richText // Optional student supervision info
}
```

## 4. News Collection
**Purpose**: Manage news, events, and announcements

```typescript
{
  // Basic Information
  title: string // News title
  slug: string // URL slug
  content: richText // Full news content
  excerpt: string // Brief summary for previews

  // Categorization
  category: select<'academic' | 'award' | 'recruitment' | 'event' | 'general'> // News category
  tags?: string[] // Optional tags for better organization

  // Visual Elements
  featuredImage: upload // Main image for the news
  gallery?: upload[] // Optional additional images

  // Publishing
  publishDate: date // Publication date
  isPublished: checkbox // Whether published
  isFeatured: checkbox // Whether featured (for homepage carousel)
  isPinned: checkbox // Whether pinned to top of news list

  // Event-specific fields (optional, for event-type news)
  eventDate?: date // Optional event date
  eventTime?: string // Optional event time
  eventLocation?: string // Optional event location
  eventRegistrationLink?: string // Optional registration link

  // SEO and Meta
  metaTitle?: string // Optional custom meta title
  metaDescription?: string // Optional custom meta description

  // Relationships
  relatedMembers?: relationship<Member[]> // Optional related members
  relatedProjects?: relationship<Project[]> // Optional related projects
  relatedResearchAreas?: relationship<ResearchArea[]> // Optional related research areas

  // System fields
  author: relationship<User> // Author/publisher
  createdAt: date // Creation timestamp
  updatedAt: date // Last update timestamp

  // Future expansion (marked as optional)
  externalLink?: string // Optional external link
  downloadableFiles?: upload[] // Optional attachments
  videoEmbed?: string // Optional video embed code
}
```

## 5. Projects Collection
**Purpose**: Manage research projects

```typescript
{
  // Basic Information
  title: string // Project title
  slug: string // URL slug
  description: richText // Detailed project description
  shortDescription: string // Brief description for cards

  // Project Status and Timeline
  status: select<'planning' | 'ongoing' | 'completed' | 'paused'> // Project status
  startDate: date // Project start date
  endDate?: date // Optional project end date

  // Visual Elements
  coverImage: upload // Main project image
  gallery?: upload[] // Optional project gallery

  // Funding and Support
  funding?: string // Optional funding information
  fundingAmount?: number // Optional funding amount
  sponsor?: string // Optional sponsor/funding agency

  // Relationships
  researchAreas: relationship<ResearchArea[]> // Related research areas
  members: relationship<Member[]> // Project team members
  principalInvestigator: relationship<Member> // Project PI
  publications?: relationship<Publication[]> // Optional related publications

  // External Links and Resources
  projectWebsite?: string // Optional project website
  githubRepo?: string // Optional GitHub repository
  documentation?: upload // Optional project documentation

  // Display Settings
  isFeatured: checkbox // Whether featured on homepage
  displayOrder: number // Display order
  isPublic: checkbox // Whether visible on public website

  // SEO
  metaTitle?: string // Optional custom meta title
  metaDescription?: string // Optional custom meta description

  // Future expansion (marked as optional)
  technologies?: string[] // Optional technologies used
  collaborators?: string[] // Optional external collaborators
  outcomes?: richText // Optional project outcomes/results
  mediaLinks?: string[] // Optional media coverage links
}
```

## 6. Publications Collection
**Purpose**: Manage academic publications and papers

```typescript
{
  // Basic Information
  title: string // Publication title
  authors: string // Author list (formatted string)
  abstract: richText // Publication abstract

  // Publication Details
  venue: string // Journal/Conference name
  year: number // Publication year
  volume?: string // Optional journal volume
  issue?: string // Optional journal issue
  pages?: string // Optional page numbers

  // Publication Type and Status
  type: select<'journal' | 'conference' | 'workshop' | 'preprint' | 'book-chapter' | 'thesis'> // Publication type
  status: select<'published' | 'accepted' | 'under-review' | 'preprint'> // Publication status

  // Identifiers and Links
  doi?: string // Optional DOI
  isbn?: string // Optional ISBN (for books)
  url?: string // Optional publication URL

  // Files and Resources
  pdfFile?: upload // Optional PDF file
  supplementaryMaterials?: upload[] // Optional supplementary files

  // Metrics and Impact
  citationCount?: number // Optional citation count
  impactFactor?: number // Optional journal impact factor

  // Keywords and Classification
  keywords: string[] // Publication keywords
  subjects?: string[] // Optional subject classifications

  // Relationships
  relatedProjects?: relationship<Project[]> // Optional related projects
  relatedMembers: relationship<Member[]> // Authors who are lab members
  researchAreas: relationship<ResearchArea[]> // Related research areas

  // Display Settings
  isFeatured: checkbox // Whether featured
  displayOrder?: number // Optional display order
  isPublic: checkbox // Whether visible on public website

  // Future expansion (marked as optional)
  awards?: string[] // Optional publication awards
  mediaAttention?: richText // Optional media coverage
  codeRepository?: string // Optional code repository link
  datasetLinks?: string[] // Optional dataset links
}
```

## 7. Resources Collection
**Purpose**: Manage downloadable resources, datasets, and tools

```typescript
{
  // Basic Information
  title: string // Resource title
  description: richText // Resource description
  shortDescription: string // Brief description for listings

  // Resource Classification
  category: select<'dataset' | 'software' | 'documentation' | 'teaching-material' | 'tool' | 'other'> // Resource category
  tags?: string[] // Optional tags for organization

  // Files and Links
  resourceFile?: upload // Optional file to download
  externalLink?: string // Optional external link (for tools/software not hosted locally)
  githubRepo?: string // Optional GitHub repository

  // Access Control
  accessLevel: select<'public' | 'members-only' | 'restricted'> // Access level
  requiresRegistration: checkbox // Whether registration is required
  licenseType?: string // Optional license information

  // Technical Details
  fileSize?: string // Optional file size information
  fileFormat?: string // Optional file format
  version?: string // Optional version number
  lastUpdated: date // Last update date

  // Usage Information
  downloadCount?: number // Optional download counter
  usageInstructions?: richText // Optional usage instructions
  systemRequirements?: string // Optional system requirements

  // Relationships
  relatedProjects?: relationship<Project[]> // Optional related projects
  relatedPublications?: relationship<Publication[]> // Optional related publications
  relatedMembers?: relationship<Member[]> // Optional related members
  researchAreas?: relationship<ResearchArea[]> // Optional related research areas

  // Display Settings
  isFeatured: checkbox // Whether featured
  displayOrder: number // Display order
  isActive: checkbox // Whether active/available

  // Future expansion (marked as optional)
  citationInfo?: richText // Optional citation information
  changelog?: richText // Optional version changelog
  supportContact?: string // Optional support contact
  tutorialLinks?: string[] // Optional tutorial links
}
```

## 8. Users Collection
**Purpose**: Manage admin users and permissions

```typescript
{
  // Authentication
  email: string // User email (unique)
  password: string // Encrypted password

  // User Information
  firstName: string // First name
  lastName: string // Last name
  avatar?: upload // Optional user avatar

  // Permissions and Roles
  roles: select<'admin' | 'editor' | 'author' | 'viewer'>[] // User roles (multiple selection)
  permissions?: string[] // Optional specific permissions

  // Account Status
  isActive: checkbox // Whether account is active
  emailVerified: checkbox // Whether email is verified
  lastLogin?: date // Optional last login timestamp

  // Contact Information
  phone?: string // Optional phone number
  department?: string // Optional department/affiliation

  // Preferences
  preferredLanguage?: select<'en' | 'zh'> // Optional language preference
  timezone?: string // Optional timezone setting

  // System Fields
  createdAt: date // Account creation date
  updatedAt: date // Last update timestamp

  // Future expansion (marked as optional)
  twoFactorEnabled?: checkbox // Optional 2FA setting
  apiKey?: string // Optional API key for integrations
  notificationPreferences?: json // Optional notification settings
}
```

## 9. Media Collection (Built-in Payload Collection)
**Purpose**: Manage uploaded files and media
*Note: This is typically handled by Payload's built-in uploads functionality, but here's the structure for reference:*

```typescript
{
  // File Information
  filename: string // Original filename
  mimeType: string // File MIME type
  filesize: number // File size in bytes
  width?: number // Optional image width
  height?: number // Optional image height

  // Storage
  url: string // File URL
  thumbnailURL?: string // Optional thumbnail URL

  // Metadata
  alt?: string // Optional alt text for images
  caption?: string // Optional caption
  description?: string // Optional description

  // System Fields
  createdAt: date // Upload timestamp
  updatedAt: date // Last update timestamp

  // Future expansion (marked as optional)
  tags?: string[] // Optional tags for organization
  copyright?: string // Optional copyright information
  exifData?: json // Optional EXIF data for images
}
```

## Collection Relationships Summary

### Key Relationships:
1. **Members** ↔ **Projects** (many-to-many)
2. **Members** ↔ **Publications** (many-to-many)
3. **Members** ↔ **Research Areas** (many-to-many)
4. **Projects** ↔ **Research Areas** (many-to-many)
5. **Projects** ↔ **Publications** (many-to-many)
6. **Publications** ↔ **Research Areas** (many-to-many)
7. **Resources** ↔ **Projects** (many-to-many)
8. **News** ↔ **Members/Projects/Research Areas** (many-to-many)

### Notes on Optional Fields:
- Fields marked with `?` are optional and can be added later without breaking existing content
- Many fields are marked as optional to allow for gradual content population
- Future expansion fields are specifically noted as potentially useful but not immediately necessary
- All collections include SEO-related fields for better search engine optimization
- Display order and featured flags are included for flexible content presentation
```
```
