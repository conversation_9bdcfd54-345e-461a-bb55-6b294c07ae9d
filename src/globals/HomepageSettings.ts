import type { GlobalConfig } from 'payload'

export const HomepageSettings: GlobalConfig = {
  slug: 'homepage-settings',
  label: 'Homepage Settings',
  access: {
    read: () => true,
  },
  fields: [
    {
      type: 'tabs',
      tabs: [
        {
          label: 'Lab Introduction',
          fields: [
            {
              name: 'labIntroTitle',
              type: 'text',
              required: true,
              label: 'Lab Introduction Title',
            },
            {
              name: 'labIntroSubtitle',
              type: 'text',
              label: 'Lab Introduction Subtitle',
            },
            {
              name: 'labIntroDescription',
              type: 'richText',
              required: true,
              label: 'Lab Description Content',
            },
            {
              name: 'labIntroBackgroundImage',
              type: 'upload',
              relationTo: 'media',
              required: true,
              label: 'Banner Background Image',
            },
            {
              name: 'labIntroVideo',
              type: 'upload',
              relationTo: 'media',
              label: 'Optional Intro Video',
              admin: {
                description: 'Optional intro video (may not be immediately needed)',
              },
            },
          ],
        },
        {
          label: 'Featured Content',
          fields: [
            {
              name: 'featuredNews',
              type: 'relationship',
              relationTo: 'news',
              hasMany: true,
              maxRows: 6,
              label: 'Featured News for Carousel',
              admin: {
                description: 'Select up to 6 news items for homepage carousel',
              },
            },
            {
              name: 'featuredResearchAreas',
              type: 'relationship',
              relationTo: 'research-areas',
              hasMany: true,
              label: 'Featured Research Areas',
              admin: {
                description: 'Research areas to display on homepage',
              },
            },
            {
              name: 'featuredProjectsEnabled',
              type: 'checkbox',
              defaultValue: false,
              label: 'Show Featured Projects Section',
            },
            {
              name: 'featuredProjects',
              type: 'relationship',
              relationTo: 'projects',
              hasMany: true,
              label: 'Featured Projects',
              admin: {
                condition: (data) => data.featuredProjectsEnabled,
                description: 'Projects to display on homepage',
              },
            },
          ],
        },
        {
          label: 'Contact Information',
          fields: [
            {
              name: 'contactEnabled',
              type: 'checkbox',
              defaultValue: false,
              label: 'Show Contact Section',
            },
            {
              name: 'contactTitle',
              type: 'text',
              label: 'Contact Section Title',
              admin: {
                condition: (data) => data.contactEnabled,
              },
            },
            {
              name: 'contactAddress',
              type: 'textarea',
              label: 'Lab Address',
              admin: {
                condition: (data) => data.contactEnabled,
              },
            },
            {
              name: 'contactPhone',
              type: 'text',
              label: 'Contact Phone',
              admin: {
                condition: (data) => data.contactEnabled,
              },
            },
            {
              name: 'contactEmail',
              type: 'email',
              label: 'Contact Email',
              admin: {
                condition: (data) => data.contactEnabled,
              },
            },
            {
              name: 'contactMapEmbed',
              type: 'textarea',
              label: 'Google Maps Embed Code',
              admin: {
                condition: (data) => data.contactEnabled,
                description: 'Optional Google Maps embed code',
              },
            },
          ],
        },
        {
          label: 'Site Settings',
          fields: [
            {
              name: 'siteTitle',
              type: 'text',
              required: true,
              label: 'Site Title',
              admin: {
                description: 'Site title for SEO and browser tab',
              },
            },
            {
              name: 'siteDescription',
              type: 'textarea',
              required: true,
              label: 'Site Description',
              admin: {
                description: 'Site description for SEO',
              },
            },
            {
              name: 'siteLogo',
              type: 'upload',
              relationTo: 'media',
              required: true,
              label: 'Site Logo',
            },
            {
              name: 'siteFavicon',
              type: 'upload',
              relationTo: 'media',
              required: true,
              label: 'Site Favicon',
            },
          ],
        },
      ],
    },
  ],
}
