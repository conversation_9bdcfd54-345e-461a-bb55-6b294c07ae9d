import type { CollectionConfig } from 'payload'

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    useAsTitle: 'email',
  },
  auth: true,
  fields: [
    // Email added by default
    {
      name: 'firstName',
      type: 'text',
      required: true,
    },
    {
      name: 'lastName',
      type: 'text',
      required: true,
    },
    {
      name: 'avatar',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'roles',
      type: 'select',
      hasMany: true,
      options: [
        { label: 'Admin', value: 'admin' },
        { label: 'Editor', value: 'editor' },
        { label: 'Author', value: 'author' },
        { label: 'Viewer', value: 'viewer' },
      ],
      defaultValue: ['viewer'],
      required: true,
    },
    {
      name: 'permissions',
      type: 'array',
      fields: [
        {
          name: 'permission',
          type: 'text',
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'emailVerified',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'lastLogin',
      type: 'date',
    },
    {
      name: 'phone',
      type: 'text',
    },
    {
      name: 'department',
      type: 'text',
    },
    {
      name: 'preferredLanguage',
      type: 'select',
      options: [
        { label: 'English', value: 'en' },
        { label: '中文', value: 'zh' },
      ],
      defaultValue: 'en',
    },
    {
      name: 'timezone',
      type: 'text',
    },
    {
      name: 'twoFactorEnabled',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'apiKey',
      type: 'text',
    },
    {
      name: 'notificationPreferences',
      type: 'json',
    },
  ],
}
