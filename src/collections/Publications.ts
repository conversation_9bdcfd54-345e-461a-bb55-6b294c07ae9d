import type { CollectionConfig } from 'payload'

export const Publications: CollectionConfig = {
  slug: 'publications',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'type', 'year', 'venue', 'status'],
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Publication Title',
    },
    {
      name: 'authors',
      type: 'text',
      required: true,
      label: 'Author List',
      admin: {
        description: 'Formatted author list string',
      },
    },
    {
      name: 'abstract',
      type: 'richText',
      required: true,
      label: 'Publication Abstract',
    },
    {
      name: 'venue',
      type: 'text',
      required: true,
      label: 'Journal/Conference Name',
    },
    {
      name: 'year',
      type: 'number',
      required: true,
      label: 'Publication Year',
    },
    {
      name: 'volume',
      type: 'text',
      label: 'Journal Volume',
    },
    {
      name: 'issue',
      type: 'text',
      label: 'Journal Issue',
    },
    {
      name: 'pages',
      type: 'text',
      label: 'Page Numbers',
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Journal Article', value: 'journal' },
        { label: 'Conference Paper', value: 'conference' },
        { label: 'Workshop Paper', value: 'workshop' },
        { label: 'Preprint', value: 'preprint' },
        { label: 'Book Chapter', value: 'book-chapter' },
        { label: 'Thesis', value: 'thesis' },
      ],
      label: 'Publication Type',
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        { label: 'Published', value: 'published' },
        { label: 'Accepted', value: 'accepted' },
        { label: 'Under Review', value: 'under-review' },
        { label: 'Preprint', value: 'preprint' },
      ],
      defaultValue: 'published',
      label: 'Publication Status',
    },
    {
      name: 'doi',
      type: 'text',
      label: 'DOI',
    },
    {
      name: 'isbn',
      type: 'text',
      label: 'ISBN',
      admin: {
        description: 'For books and book chapters',
      },
    },
    {
      name: 'url',
      type: 'text',
      label: 'Publication URL',
    },
    {
      name: 'pdfFile',
      type: 'upload',
      relationTo: 'media',
      label: 'PDF File',
    },
    {
      name: 'supplementaryMaterials',
      type: 'array',
      label: 'Supplementary Materials',
      fields: [
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
      ],
    },
    {
      name: 'citationCount',
      type: 'number',
      label: 'Citation Count',
    },
    {
      name: 'impactFactor',
      type: 'number',
      label: 'Journal Impact Factor',
    },
    {
      name: 'keywords',
      type: 'array',
      required: true,
      label: 'Keywords',
      fields: [
        {
          name: 'keyword',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'subjects',
      type: 'array',
      label: 'Subject Classifications',
      fields: [
        {
          name: 'subject',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'relatedProjects',
      type: 'relationship',
      relationTo: 'projects',
      hasMany: true,
      label: 'Related Projects',
    },
    {
      name: 'relatedMembers',
      type: 'relationship',
      relationTo: 'members',
      hasMany: true,
      required: true,
      label: 'Lab Member Authors',
    },
    {
      name: 'researchAreas',
      type: 'relationship',
      relationTo: 'research-areas',
      hasMany: true,
      required: true,
      label: 'Related Research Areas',
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
      label: 'Featured Publication',
    },
    {
      name: 'displayOrder',
      type: 'number',
      defaultValue: 0,
      label: 'Display Order',
    },
    {
      name: 'isPublic',
      type: 'checkbox',
      defaultValue: true,
      label: 'Visible on Public Website',
    },
    {
      name: 'awards',
      type: 'array',
      label: 'Publication Awards',
      fields: [
        {
          name: 'award',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'mediaAttention',
      type: 'richText',
      label: 'Media Coverage',
    },
    {
      name: 'codeRepository',
      type: 'text',
      label: 'Code Repository Link',
    },
    {
      name: 'datasetLinks',
      type: 'array',
      label: 'Dataset Links',
      fields: [
        {
          name: 'link',
          type: 'text',
          required: true,
        },
      ],
    },
  ],
}
