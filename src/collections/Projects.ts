import type { CollectionConfig } from 'payload'

export const Projects: CollectionConfig = {
  slug: 'projects',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'status', 'startDate', 'principalInvestigator', 'isFeatured'],
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Project Title',
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'URL Slug',
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      label: 'Detailed Project Description',
    },
    {
      name: 'shortDescription',
      type: 'textarea',
      required: true,
      label: 'Brief Description',
      admin: {
        description: 'Brief description for cards and previews',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        { label: 'Planning', value: 'planning' },
        { label: 'Ongoing', value: 'ongoing' },
        { label: 'Completed', value: 'completed' },
        { label: 'Paused', value: 'paused' },
      ],
      defaultValue: 'planning',
      label: 'Project Status',
    },
    {
      name: 'startDate',
      type: 'date',
      required: true,
      label: 'Project Start Date',
    },
    {
      name: 'endDate',
      type: 'date',
      label: 'Project End Date',
    },
    {
      name: 'coverImage',
      type: 'upload',
      relationTo: 'media',
      required: true,
      label: 'Main Project Image',
    },
    {
      name: 'gallery',
      type: 'array',
      label: 'Project Gallery',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
      ],
    },
    {
      name: 'funding',
      type: 'text',
      label: 'Funding Information',
    },
    {
      name: 'fundingAmount',
      type: 'number',
      label: 'Funding Amount',
    },
    {
      name: 'sponsor',
      type: 'text',
      label: 'Sponsor/Funding Agency',
    },
    {
      name: 'researchAreas',
      type: 'relationship',
      relationTo: 'research-areas',
      hasMany: true,
      required: true,
      label: 'Related Research Areas',
    },
    {
      name: 'members',
      type: 'relationship',
      relationTo: 'members',
      hasMany: true,
      required: true,
      label: 'Project Team Members',
    },
    {
      name: 'principalInvestigator',
      type: 'relationship',
      relationTo: 'members',
      required: true,
      label: 'Principal Investigator',
    },
    {
      name: 'publications',
      type: 'relationship',
      relationTo: 'publications',
      hasMany: true,
      label: 'Related Publications',
    },
    {
      name: 'projectWebsite',
      type: 'text',
      label: 'Project Website',
    },
    {
      name: 'githubRepo',
      type: 'text',
      label: 'GitHub Repository',
    },
    {
      name: 'documentation',
      type: 'upload',
      relationTo: 'media',
      label: 'Project Documentation',
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
      label: 'Featured on Homepage',
    },
    {
      name: 'displayOrder',
      type: 'number',
      required: true,
      defaultValue: 0,
      label: 'Display Order',
    },
    {
      name: 'isPublic',
      type: 'checkbox',
      defaultValue: true,
      label: 'Visible on Public Website',
    },
    {
      name: 'metaTitle',
      type: 'text',
      label: 'Meta Title',
      admin: {
        description: 'Optional custom meta title for SEO',
      },
    },
    {
      name: 'metaDescription',
      type: 'textarea',
      label: 'Meta Description',
      admin: {
        description: 'Optional custom meta description for SEO',
      },
    },
    {
      name: 'technologies',
      type: 'array',
      label: 'Technologies Used',
      fields: [
        {
          name: 'technology',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'collaborators',
      type: 'array',
      label: 'External Collaborators',
      fields: [
        {
          name: 'collaborator',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'outcomes',
      type: 'richText',
      label: 'Project Outcomes/Results',
    },
    {
      name: 'mediaLinks',
      type: 'array',
      label: 'Media Coverage Links',
      fields: [
        {
          name: 'link',
          type: 'text',
          required: true,
        },
      ],
    },
  ],
}
