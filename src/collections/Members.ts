import type { CollectionConfig } from 'payload'

export const Members: CollectionConfig = {
  slug: 'members',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'role', 'isActive', 'displayOrder'],
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Full Name',
    },
    {
      name: 'email',
      type: 'email',
      required: true,
      label: 'Email Address',
    },
    {
      name: 'phone',
      type: 'text',
      label: 'Phone Number',
    },
    {
      name: 'role',
      type: 'select',
      required: true,
      options: [
        { label: 'Faculty Member', value: 'faculty' },
        { label: 'Postdoc/PhD Student', value: 'postdoc' },
        { label: 'Graduate Student', value: 'master' },
        { label: 'Undergraduate Student', value: 'undergraduate' },
        { label: 'Alumni', value: 'alumni' },
      ],
      label: 'Member Category',
    },
    {
      name: 'title',
      type: 'text',
      label: 'Professional Title',
      admin: {
        description: 'e.g., Professor, Associate Professor, etc.',
      },
    },
    {
      name: 'position',
      type: 'text',
      required: true,
      label: 'Current Position',
    },
    {
      name: 'photo',
      type: 'upload',
      relationTo: 'media',
      required: true,
      label: 'Member Photo',
    },
    {
      name: 'bio',
      type: 'richText',
      required: true,
      label: 'Biography and Research Interests',
    },
    {
      name: 'shortBio',
      type: 'textarea',
      label: 'Brief Bio',
      admin: {
        description: 'Brief bio for cards and previews',
      },
    },
    {
      name: 'researchInterests',
      type: 'array',
      required: true,
      label: 'Research Interests',
      fields: [
        {
          name: 'interest',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'education',
      type: 'richText',
      label: 'Educational Background',
    },
    {
      name: 'experience',
      type: 'richText',
      label: 'Work Experience',
    },
    {
      name: 'homepage',
      type: 'text',
      label: 'Personal Homepage',
    },
    {
      name: 'googleScholar',
      type: 'text',
      label: 'Google Scholar Profile',
    },
    {
      name: 'orcid',
      type: 'text',
      label: 'ORCID ID',
    },
    {
      name: 'linkedin',
      type: 'text',
      label: 'LinkedIn Profile',
    },
    {
      name: 'github',
      type: 'text',
      label: 'GitHub Profile',
    },
    {
      name: 'joinDate',
      type: 'date',
      required: true,
      label: 'Date Joined Lab',
    },
    {
      name: 'graduationDate',
      type: 'date',
      label: 'Graduation Date',
      admin: {
        description: 'For students and alumni',
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      label: 'Currently Active Member',
    },
    {
      name: 'displayOrder',
      type: 'number',
      required: true,
      defaultValue: 0,
      label: 'Display Order',
    },
    {
      name: 'showOnWebsite',
      type: 'checkbox',
      defaultValue: true,
      label: 'Show on Public Website',
    },
    {
      name: 'researchAreas',
      type: 'relationship',
      relationTo: 'research-areas',
      hasMany: true,
      label: 'Research Areas of Interest',
    },
    {
      name: 'projects',
      type: 'relationship',
      relationTo: 'projects',
      hasMany: true,
      label: 'Projects Involved In',
    },
    {
      name: 'publications',
      type: 'relationship',
      relationTo: 'publications',
      hasMany: true,
      label: 'Publications Authored',
    },
    {
      name: 'awards',
      type: 'richText',
      label: 'Awards and Honors',
    },
    {
      name: 'teachingCourses',
      type: 'array',
      label: 'Teaching Courses',
      fields: [
        {
          name: 'course',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'supervisionInfo',
      type: 'richText',
      label: 'Student Supervision Information',
    },
  ],
}
