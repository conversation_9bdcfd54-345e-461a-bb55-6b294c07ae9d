import type { CollectionConfig } from 'payload'

export const Resources: CollectionConfig = {
  slug: 'resources',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'accessLevel', 'isActive', 'lastUpdated'],
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Resource Title',
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      label: 'Resource Description',
    },
    {
      name: 'shortDescription',
      type: 'textarea',
      required: true,
      label: 'Brief Description',
      admin: {
        description: 'Brief description for listings',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Dataset', value: 'dataset' },
        { label: 'Software', value: 'software' },
        { label: 'Documentation', value: 'documentation' },
        { label: 'Teaching Material', value: 'teaching-material' },
        { label: 'Tool', value: 'tool' },
        { label: 'Other', value: 'other' },
      ],
      label: 'Resource Category',
    },
    {
      name: 'tags',
      type: 'array',
      label: 'Tags',
      fields: [
        {
          name: 'tag',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'resourceFile',
      type: 'upload',
      relationTo: 'media',
      label: 'Resource File',
      admin: {
        description: 'File to download (optional if external link is provided)',
      },
    },
    {
      name: 'externalLink',
      type: 'text',
      label: 'External Link',
      admin: {
        description: 'For tools/software not hosted locally',
      },
    },
    {
      name: 'githubRepo',
      type: 'text',
      label: 'GitHub Repository',
    },
    {
      name: 'accessLevel',
      type: 'select',
      required: true,
      options: [
        { label: 'Public', value: 'public' },
        { label: 'Members Only', value: 'members-only' },
        { label: 'Restricted', value: 'restricted' },
      ],
      defaultValue: 'public',
      label: 'Access Level',
    },
    {
      name: 'requiresRegistration',
      type: 'checkbox',
      defaultValue: false,
      label: 'Requires Registration',
    },
    {
      name: 'licenseType',
      type: 'text',
      label: 'License Information',
    },
    {
      name: 'fileSize',
      type: 'text',
      label: 'File Size',
    },
    {
      name: 'fileFormat',
      type: 'text',
      label: 'File Format',
    },
    {
      name: 'version',
      type: 'text',
      label: 'Version Number',
    },
    {
      name: 'lastUpdated',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
      label: 'Last Updated',
    },
    {
      name: 'downloadCount',
      type: 'number',
      defaultValue: 0,
      label: 'Download Count',
    },
    {
      name: 'usageInstructions',
      type: 'richText',
      label: 'Usage Instructions',
    },
    {
      name: 'systemRequirements',
      type: 'text',
      label: 'System Requirements',
    },
    {
      name: 'relatedProjects',
      type: 'relationship',
      relationTo: 'projects',
      hasMany: true,
      label: 'Related Projects',
    },
    {
      name: 'relatedPublications',
      type: 'relationship',
      relationTo: 'publications',
      hasMany: true,
      label: 'Related Publications',
    },
    {
      name: 'relatedMembers',
      type: 'relationship',
      relationTo: 'members',
      hasMany: true,
      label: 'Related Members',
    },
    {
      name: 'researchAreas',
      type: 'relationship',
      relationTo: 'research-areas',
      hasMany: true,
      label: 'Related Research Areas',
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
      label: 'Featured Resource',
    },
    {
      name: 'displayOrder',
      type: 'number',
      required: true,
      defaultValue: 0,
      label: 'Display Order',
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      label: 'Active/Available',
    },
    {
      name: 'citationInfo',
      type: 'richText',
      label: 'Citation Information',
    },
    {
      name: 'changelog',
      type: 'richText',
      label: 'Version Changelog',
    },
    {
      name: 'supportContact',
      type: 'text',
      label: 'Support Contact',
    },
    {
      name: 'tutorialLinks',
      type: 'array',
      label: 'Tutorial Links',
      fields: [
        {
          name: 'link',
          type: 'text',
          required: true,
        },
      ],
    },
  ],
}
