import type { CollectionConfig } from 'payload'

export const News: CollectionConfig = {
  slug: 'news',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'publishDate', 'isPublished', 'isFeatured'],
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'News Title',
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'URL Slug',
    },
    {
      name: 'content',
      type: 'richText',
      required: true,
      label: 'Full News Content',
    },
    {
      name: 'excerpt',
      type: 'textarea',
      required: true,
      label: 'Brief Summary',
      admin: {
        description: 'Brief summary for previews and listings',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Academic Activity', value: 'academic' },
        { label: 'Award Information', value: 'award' },
        { label: 'Recruitment', value: 'recruitment' },
        { label: 'Event', value: 'event' },
        { label: 'General', value: 'general' },
      ],
      label: 'News Category',
    },
    {
      name: 'tags',
      type: 'array',
      label: 'Tags',
      fields: [
        {
          name: 'tag',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      required: true,
      label: 'Featured Image',
    },
    {
      name: 'gallery',
      type: 'array',
      label: 'Additional Images',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
      ],
    },
    {
      name: 'publishDate',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
      label: 'Publication Date',
    },
    {
      name: 'isPublished',
      type: 'checkbox',
      defaultValue: false,
      label: 'Published',
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
      label: 'Featured (Homepage Carousel)',
    },
    {
      name: 'isPinned',
      type: 'checkbox',
      defaultValue: false,
      label: 'Pinned to Top',
    },
    {
      name: 'eventDate',
      type: 'date',
      label: 'Event Date',
      admin: {
        description: 'For event-type news',
      },
    },
    {
      name: 'eventTime',
      type: 'text',
      label: 'Event Time',
    },
    {
      name: 'eventLocation',
      type: 'text',
      label: 'Event Location',
    },
    {
      name: 'eventRegistrationLink',
      type: 'text',
      label: 'Event Registration Link',
    },
    {
      name: 'metaTitle',
      type: 'text',
      label: 'Meta Title',
      admin: {
        description: 'Optional custom meta title for SEO',
      },
    },
    {
      name: 'metaDescription',
      type: 'textarea',
      label: 'Meta Description',
      admin: {
        description: 'Optional custom meta description for SEO',
      },
    },
    {
      name: 'relatedMembers',
      type: 'relationship',
      relationTo: 'members',
      hasMany: true,
      label: 'Related Members',
    },
    {
      name: 'relatedProjects',
      type: 'relationship',
      relationTo: 'projects',
      hasMany: true,
      label: 'Related Projects',
    },
    {
      name: 'relatedResearchAreas',
      type: 'relationship',
      relationTo: 'research-areas',
      hasMany: true,
      label: 'Related Research Areas',
    },
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      label: 'Author/Publisher',
    },
    {
      name: 'externalLink',
      type: 'text',
      label: 'External Link',
    },
    {
      name: 'downloadableFiles',
      type: 'array',
      label: 'Downloadable Files',
      fields: [
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
      ],
    },
    {
      name: 'videoEmbed',
      type: 'textarea',
      label: 'Video Embed Code',
      admin: {
        description: 'Optional video embed code (YouTube, Vimeo, etc.)',
      },
    },
  ],
}
