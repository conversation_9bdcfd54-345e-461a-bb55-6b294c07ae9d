import type { CollectionConfig } from 'payload'

export const ResearchAreas: CollectionConfig = {
  slug: 'research-areas',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'isActive', 'isFeatured', 'order'],
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Research Area Name',
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'URL Slug',
      admin: {
        description: 'Used in the URL for this research area',
      },
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      label: 'Detailed Description',
    },
    {
      name: 'shortDescription',
      type: 'textarea',
      required: true,
      label: 'Brief Description',
      admin: {
        description: 'Brief description for cards and previews',
      },
    },
    {
      name: 'icon',
      type: 'upload',
      relationTo: 'media',
      label: 'Research Area Icon',
    },
    {
      name: 'coverImage',
      type: 'upload',
      relationTo: 'media',
      label: 'Cover Image',
    },
    {
      name: 'color',
      type: 'text',
      label: 'Theme Color',
      admin: {
        description: 'Optional theme color for the research area (hex code)',
      },
    },
    {
      name: 'order',
      type: 'number',
      required: true,
      defaultValue: 0,
      label: 'Display Order',
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      label: 'Active',
      admin: {
        description: 'Whether to display this research area',
      },
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
      label: 'Featured on Homepage',
    },
    {
      name: 'relatedProjects',
      type: 'relationship',
      relationTo: 'projects',
      hasMany: true,
      label: 'Related Projects',
    },
    {
      name: 'relatedPublications',
      type: 'relationship',
      relationTo: 'publications',
      hasMany: true,
      label: 'Related Publications',
    },
    {
      name: 'relatedMembers',
      type: 'relationship',
      relationTo: 'members',
      hasMany: true,
      label: 'Related Members',
    },
    {
      name: 'metaTitle',
      type: 'text',
      label: 'Meta Title',
      admin: {
        description: 'Optional custom meta title for SEO',
      },
    },
    {
      name: 'metaDescription',
      type: 'textarea',
      label: 'Meta Description',
      admin: {
        description: 'Optional custom meta description for SEO',
      },
    },
    {
      name: 'researchGoals',
      type: 'richText',
      label: 'Research Goals',
      admin: {
        description: 'Optional detailed research goals',
      },
    },
    {
      name: 'keyTechnologies',
      type: 'array',
      label: 'Key Technologies',
      fields: [
        {
          name: 'technology',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'collaborations',
      type: 'array',
      label: 'External Collaborations',
      fields: [
        {
          name: 'collaboration',
          type: 'text',
          required: true,
        },
      ],
    },
  ],
}
