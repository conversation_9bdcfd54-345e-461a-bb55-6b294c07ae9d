/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    'research-areas': ResearchArea;
    members: Member;
    news: News;
    projects: Project;
    publications: Publication;
    resources: Resource;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    'research-areas': ResearchAreasSelect<false> | ResearchAreasSelect<true>;
    members: MembersSelect<false> | MembersSelect<true>;
    news: NewsSelect<false> | NewsSelect<true>;
    projects: ProjectsSelect<false> | ProjectsSelect<true>;
    publications: PublicationsSelect<false> | PublicationsSelect<true>;
    resources: ResourcesSelect<false> | ResourcesSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {
    'homepage-settings': HomepageSetting;
  };
  globalsSelect: {
    'homepage-settings': HomepageSettingsSelect<false> | HomepageSettingsSelect<true>;
  };
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  firstName: string;
  lastName: string;
  avatar?: (number | null) | Media;
  roles: ('admin' | 'editor' | 'author' | 'viewer')[];
  permissions?:
    | {
        permission?: string | null;
        id?: string | null;
      }[]
    | null;
  isActive?: boolean | null;
  emailVerified?: boolean | null;
  lastLogin?: string | null;
  phone?: string | null;
  department?: string | null;
  preferredLanguage?: ('en' | 'zh') | null;
  timezone?: string | null;
  twoFactorEnabled?: boolean | null;
  apiKey?: string | null;
  notificationPreferences?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  alt: string;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "research-areas".
 */
export interface ResearchArea {
  id: number;
  name: string;
  /**
   * Used in the URL for this research area
   */
  slug: string;
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Brief description for cards and previews
   */
  shortDescription: string;
  icon?: (number | null) | Media;
  coverImage?: (number | null) | Media;
  /**
   * Optional theme color for the research area (hex code)
   */
  color?: string | null;
  order: number;
  /**
   * Whether to display this research area
   */
  isActive?: boolean | null;
  isFeatured?: boolean | null;
  relatedProjects?: (number | Project)[] | null;
  relatedPublications?: (number | Publication)[] | null;
  relatedMembers?: (number | Member)[] | null;
  /**
   * Optional custom meta title for SEO
   */
  metaTitle?: string | null;
  /**
   * Optional custom meta description for SEO
   */
  metaDescription?: string | null;
  /**
   * Optional detailed research goals
   */
  researchGoals?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  keyTechnologies?:
    | {
        technology: string;
        id?: string | null;
      }[]
    | null;
  collaborations?:
    | {
        collaboration: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "projects".
 */
export interface Project {
  id: number;
  title: string;
  slug: string;
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Brief description for cards and previews
   */
  shortDescription: string;
  status: 'planning' | 'ongoing' | 'completed' | 'paused';
  startDate: string;
  endDate?: string | null;
  coverImage: number | Media;
  gallery?:
    | {
        image: number | Media;
        id?: string | null;
      }[]
    | null;
  funding?: string | null;
  fundingAmount?: number | null;
  sponsor?: string | null;
  researchAreas: (number | ResearchArea)[];
  members: (number | Member)[];
  principalInvestigator: number | Member;
  publications?: (number | Publication)[] | null;
  projectWebsite?: string | null;
  githubRepo?: string | null;
  documentation?: (number | null) | Media;
  isFeatured?: boolean | null;
  displayOrder: number;
  isPublic?: boolean | null;
  /**
   * Optional custom meta title for SEO
   */
  metaTitle?: string | null;
  /**
   * Optional custom meta description for SEO
   */
  metaDescription?: string | null;
  technologies?:
    | {
        technology: string;
        id?: string | null;
      }[]
    | null;
  collaborators?:
    | {
        collaborator: string;
        id?: string | null;
      }[]
    | null;
  outcomes?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  mediaLinks?:
    | {
        link: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "members".
 */
export interface Member {
  id: number;
  name: string;
  email: string;
  phone?: string | null;
  role: 'faculty' | 'postdoc' | 'master' | 'undergraduate' | 'alumni';
  /**
   * e.g., Professor, Associate Professor, etc.
   */
  title?: string | null;
  position: string;
  photo: number | Media;
  bio: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Brief bio for cards and previews
   */
  shortBio?: string | null;
  researchInterests: {
    interest: string;
    id?: string | null;
  }[];
  education?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  experience?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  homepage?: string | null;
  googleScholar?: string | null;
  orcid?: string | null;
  linkedin?: string | null;
  github?: string | null;
  joinDate: string;
  /**
   * For students and alumni
   */
  graduationDate?: string | null;
  isActive?: boolean | null;
  displayOrder: number;
  showOnWebsite?: boolean | null;
  researchAreas?: (number | ResearchArea)[] | null;
  projects?: (number | Project)[] | null;
  publications?: (number | Publication)[] | null;
  awards?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  teachingCourses?:
    | {
        course: string;
        id?: string | null;
      }[]
    | null;
  supervisionInfo?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "publications".
 */
export interface Publication {
  id: number;
  title: string;
  /**
   * Formatted author list string
   */
  authors: string;
  abstract: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  venue: string;
  year: number;
  volume?: string | null;
  issue?: string | null;
  pages?: string | null;
  type: 'journal' | 'conference' | 'workshop' | 'preprint' | 'book-chapter' | 'thesis';
  status: 'published' | 'accepted' | 'under-review' | 'preprint';
  doi?: string | null;
  /**
   * For books and book chapters
   */
  isbn?: string | null;
  url?: string | null;
  pdfFile?: (number | null) | Media;
  supplementaryMaterials?:
    | {
        file: number | Media;
        id?: string | null;
      }[]
    | null;
  citationCount?: number | null;
  impactFactor?: number | null;
  keywords: {
    keyword: string;
    id?: string | null;
  }[];
  subjects?:
    | {
        subject: string;
        id?: string | null;
      }[]
    | null;
  relatedProjects?: (number | Project)[] | null;
  relatedMembers: (number | Member)[];
  researchAreas: (number | ResearchArea)[];
  isFeatured?: boolean | null;
  displayOrder?: number | null;
  isPublic?: boolean | null;
  awards?:
    | {
        award: string;
        id?: string | null;
      }[]
    | null;
  mediaAttention?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  codeRepository?: string | null;
  datasetLinks?:
    | {
        link: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "news".
 */
export interface News {
  id: number;
  title: string;
  slug: string;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Brief summary for previews and listings
   */
  excerpt: string;
  category: 'academic' | 'award' | 'recruitment' | 'event' | 'general';
  tags?:
    | {
        tag: string;
        id?: string | null;
      }[]
    | null;
  featuredImage: number | Media;
  gallery?:
    | {
        image: number | Media;
        id?: string | null;
      }[]
    | null;
  publishDate: string;
  isPublished?: boolean | null;
  isFeatured?: boolean | null;
  isPinned?: boolean | null;
  /**
   * For event-type news
   */
  eventDate?: string | null;
  eventTime?: string | null;
  eventLocation?: string | null;
  eventRegistrationLink?: string | null;
  /**
   * Optional custom meta title for SEO
   */
  metaTitle?: string | null;
  /**
   * Optional custom meta description for SEO
   */
  metaDescription?: string | null;
  relatedMembers?: (number | Member)[] | null;
  relatedProjects?: (number | Project)[] | null;
  relatedResearchAreas?: (number | ResearchArea)[] | null;
  author: number | User;
  externalLink?: string | null;
  downloadableFiles?:
    | {
        file: number | Media;
        id?: string | null;
      }[]
    | null;
  /**
   * Optional video embed code (YouTube, Vimeo, etc.)
   */
  videoEmbed?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "resources".
 */
export interface Resource {
  id: number;
  title: string;
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Brief description for listings
   */
  shortDescription: string;
  category: 'dataset' | 'software' | 'documentation' | 'teaching-material' | 'tool' | 'other';
  tags?:
    | {
        tag: string;
        id?: string | null;
      }[]
    | null;
  /**
   * File to download (optional if external link is provided)
   */
  resourceFile?: (number | null) | Media;
  /**
   * For tools/software not hosted locally
   */
  externalLink?: string | null;
  githubRepo?: string | null;
  accessLevel: 'public' | 'members-only' | 'restricted';
  requiresRegistration?: boolean | null;
  licenseType?: string | null;
  fileSize?: string | null;
  fileFormat?: string | null;
  version?: string | null;
  lastUpdated: string;
  downloadCount?: number | null;
  usageInstructions?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  systemRequirements?: string | null;
  relatedProjects?: (number | Project)[] | null;
  relatedPublications?: (number | Publication)[] | null;
  relatedMembers?: (number | Member)[] | null;
  researchAreas?: (number | ResearchArea)[] | null;
  isFeatured?: boolean | null;
  displayOrder: number;
  isActive?: boolean | null;
  citationInfo?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  changelog?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  supportContact?: string | null;
  tutorialLinks?:
    | {
        link: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'research-areas';
        value: number | ResearchArea;
      } | null)
    | ({
        relationTo: 'members';
        value: number | Member;
      } | null)
    | ({
        relationTo: 'news';
        value: number | News;
      } | null)
    | ({
        relationTo: 'projects';
        value: number | Project;
      } | null)
    | ({
        relationTo: 'publications';
        value: number | Publication;
      } | null)
    | ({
        relationTo: 'resources';
        value: number | Resource;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  firstName?: T;
  lastName?: T;
  avatar?: T;
  roles?: T;
  permissions?:
    | T
    | {
        permission?: T;
        id?: T;
      };
  isActive?: T;
  emailVerified?: T;
  lastLogin?: T;
  phone?: T;
  department?: T;
  preferredLanguage?: T;
  timezone?: T;
  twoFactorEnabled?: T;
  apiKey?: T;
  notificationPreferences?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "research-areas_select".
 */
export interface ResearchAreasSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  description?: T;
  shortDescription?: T;
  icon?: T;
  coverImage?: T;
  color?: T;
  order?: T;
  isActive?: T;
  isFeatured?: T;
  relatedProjects?: T;
  relatedPublications?: T;
  relatedMembers?: T;
  metaTitle?: T;
  metaDescription?: T;
  researchGoals?: T;
  keyTechnologies?:
    | T
    | {
        technology?: T;
        id?: T;
      };
  collaborations?:
    | T
    | {
        collaboration?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "members_select".
 */
export interface MembersSelect<T extends boolean = true> {
  name?: T;
  email?: T;
  phone?: T;
  role?: T;
  title?: T;
  position?: T;
  photo?: T;
  bio?: T;
  shortBio?: T;
  researchInterests?:
    | T
    | {
        interest?: T;
        id?: T;
      };
  education?: T;
  experience?: T;
  homepage?: T;
  googleScholar?: T;
  orcid?: T;
  linkedin?: T;
  github?: T;
  joinDate?: T;
  graduationDate?: T;
  isActive?: T;
  displayOrder?: T;
  showOnWebsite?: T;
  researchAreas?: T;
  projects?: T;
  publications?: T;
  awards?: T;
  teachingCourses?:
    | T
    | {
        course?: T;
        id?: T;
      };
  supervisionInfo?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "news_select".
 */
export interface NewsSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  content?: T;
  excerpt?: T;
  category?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  featuredImage?: T;
  gallery?:
    | T
    | {
        image?: T;
        id?: T;
      };
  publishDate?: T;
  isPublished?: T;
  isFeatured?: T;
  isPinned?: T;
  eventDate?: T;
  eventTime?: T;
  eventLocation?: T;
  eventRegistrationLink?: T;
  metaTitle?: T;
  metaDescription?: T;
  relatedMembers?: T;
  relatedProjects?: T;
  relatedResearchAreas?: T;
  author?: T;
  externalLink?: T;
  downloadableFiles?:
    | T
    | {
        file?: T;
        id?: T;
      };
  videoEmbed?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "projects_select".
 */
export interface ProjectsSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  shortDescription?: T;
  status?: T;
  startDate?: T;
  endDate?: T;
  coverImage?: T;
  gallery?:
    | T
    | {
        image?: T;
        id?: T;
      };
  funding?: T;
  fundingAmount?: T;
  sponsor?: T;
  researchAreas?: T;
  members?: T;
  principalInvestigator?: T;
  publications?: T;
  projectWebsite?: T;
  githubRepo?: T;
  documentation?: T;
  isFeatured?: T;
  displayOrder?: T;
  isPublic?: T;
  metaTitle?: T;
  metaDescription?: T;
  technologies?:
    | T
    | {
        technology?: T;
        id?: T;
      };
  collaborators?:
    | T
    | {
        collaborator?: T;
        id?: T;
      };
  outcomes?: T;
  mediaLinks?:
    | T
    | {
        link?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "publications_select".
 */
export interface PublicationsSelect<T extends boolean = true> {
  title?: T;
  authors?: T;
  abstract?: T;
  venue?: T;
  year?: T;
  volume?: T;
  issue?: T;
  pages?: T;
  type?: T;
  status?: T;
  doi?: T;
  isbn?: T;
  url?: T;
  pdfFile?: T;
  supplementaryMaterials?:
    | T
    | {
        file?: T;
        id?: T;
      };
  citationCount?: T;
  impactFactor?: T;
  keywords?:
    | T
    | {
        keyword?: T;
        id?: T;
      };
  subjects?:
    | T
    | {
        subject?: T;
        id?: T;
      };
  relatedProjects?: T;
  relatedMembers?: T;
  researchAreas?: T;
  isFeatured?: T;
  displayOrder?: T;
  isPublic?: T;
  awards?:
    | T
    | {
        award?: T;
        id?: T;
      };
  mediaAttention?: T;
  codeRepository?: T;
  datasetLinks?:
    | T
    | {
        link?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "resources_select".
 */
export interface ResourcesSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  shortDescription?: T;
  category?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  resourceFile?: T;
  externalLink?: T;
  githubRepo?: T;
  accessLevel?: T;
  requiresRegistration?: T;
  licenseType?: T;
  fileSize?: T;
  fileFormat?: T;
  version?: T;
  lastUpdated?: T;
  downloadCount?: T;
  usageInstructions?: T;
  systemRequirements?: T;
  relatedProjects?: T;
  relatedPublications?: T;
  relatedMembers?: T;
  researchAreas?: T;
  isFeatured?: T;
  displayOrder?: T;
  isActive?: T;
  citationInfo?: T;
  changelog?: T;
  supportContact?: T;
  tutorialLinks?:
    | T
    | {
        link?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "homepage-settings".
 */
export interface HomepageSetting {
  id: number;
  labIntroTitle: string;
  labIntroSubtitle?: string | null;
  labIntroDescription: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  labIntroBackgroundImage: number | Media;
  /**
   * Optional intro video (may not be immediately needed)
   */
  labIntroVideo?: (number | null) | Media;
  /**
   * Select up to 6 news items for homepage carousel
   */
  featuredNews?: (number | News)[] | null;
  /**
   * Research areas to display on homepage
   */
  featuredResearchAreas?: (number | ResearchArea)[] | null;
  featuredProjectsEnabled?: boolean | null;
  /**
   * Projects to display on homepage
   */
  featuredProjects?: (number | Project)[] | null;
  contactEnabled?: boolean | null;
  contactTitle?: string | null;
  contactAddress?: string | null;
  contactPhone?: string | null;
  contactEmail?: string | null;
  /**
   * Optional Google Maps embed code
   */
  contactMapEmbed?: string | null;
  /**
   * Site title for SEO and browser tab
   */
  siteTitle: string;
  /**
   * Site description for SEO
   */
  siteDescription: string;
  siteLogo: number | Media;
  siteFavicon: number | Media;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "homepage-settings_select".
 */
export interface HomepageSettingsSelect<T extends boolean = true> {
  labIntroTitle?: T;
  labIntroSubtitle?: T;
  labIntroDescription?: T;
  labIntroBackgroundImage?: T;
  labIntroVideo?: T;
  featuredNews?: T;
  featuredResearchAreas?: T;
  featuredProjectsEnabled?: T;
  featuredProjects?: T;
  contactEnabled?: T;
  contactTitle?: T;
  contactAddress?: T;
  contactPhone?: T;
  contactEmail?: T;
  contactMapEmbed?: T;
  siteTitle?: T;
  siteDescription?: T;
  siteLogo?: T;
  siteFavicon?: T;
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}