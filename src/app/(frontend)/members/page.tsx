import { getPayload } from 'payload'
import React from 'react'
import config from '@/payload.config'
import type { Member } from '@/payload-types'

export default async function MembersPage() {
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  // Fetch members by role
  const [faculty, postdocs, masters, undergrads, alumni] = await Promise.all([
    payload.find({
      collection: 'members',
      where: {
        role: { equals: 'faculty' },
        showOnWebsite: { equals: true },
        isActive: { equals: true },
      },
      sort: 'displayOrder',
    }),
    payload.find({
      collection: 'members',
      where: {
        role: { equals: 'postdoc' },
        showOnWebsite: { equals: true },
        isActive: { equals: true },
      },
      sort: 'displayOrder',
    }),
    payload.find({
      collection: 'members',
      where: {
        role: { equals: 'master' },
        showOnWebsite: { equals: true },
        isActive: { equals: true },
      },
      sort: 'displayOrder',
    }),
    payload.find({
      collection: 'members',
      where: {
        role: { equals: 'undergraduate' },
        showOnWebsite: { equals: true },
        isActive: { equals: true },
      },
      sort: 'displayOrder',
    }),
    payload.find({
      collection: 'members',
      where: {
        role: { equals: 'alumni' },
        showOnWebsite: { equals: true },
      },
      sort: 'displayOrder',
      limit: 20, // Limit alumni to avoid too many results
    }),
  ])

  const MemberCard = ({ member }: { member: Member }) => (
    <div className="member-card">
      {member.photo && (
        <div className="member-photo">
          <img 
            src={typeof member.photo === 'object' ? member.photo.url || '' : ''} 
            alt={member.name}
          />
        </div>
      )}
      <div className="member-info">
        <h3>{member.name}</h3>
        {member.title && <p className="title">{member.title}</p>}
        <p className="position">{member.position}</p>
        {member.shortBio && <p className="bio">{member.shortBio}</p>}
        
        {member.researchInterests && member.researchInterests.length > 0 && (
          <div className="research-interests">
            <strong>Research Interests:</strong>
            <div className="interests-tags">
              {member.researchInterests.map((interest, index) => (
                <span key={index} className="interest-tag">
                  {typeof interest === 'object' ? interest.interest : interest}
                </span>
              ))}
            </div>
          </div>
        )}

        <div className="member-links">
          {member.email && (
            <a href={`mailto:${member.email}`} className="contact-link">
              Email
            </a>
          )}
          {member.homepage && (
            <a href={member.homepage} target="_blank" rel="noopener noreferrer" className="contact-link">
              Homepage
            </a>
          )}
          {member.googleScholar && (
            <a href={member.googleScholar} target="_blank" rel="noopener noreferrer" className="contact-link">
              Google Scholar
            </a>
          )}
        </div>
      </div>
    </div>
  )

  const MemberSection = ({ title, members }: { title: string; members: Member[] }) => {
    if (members.length === 0) return null
    
    return (
      <section className="member-section">
        <h2>{title}</h2>
        <div className="members-grid">
          {members.map((member) => (
            <MemberCard key={member.id} member={member} />
          ))}
        </div>
      </section>
    )
  }

  return (
    <div className="members-page">
      <div className="container">
        <header className="page-header">
          <h1>Team Members</h1>
          <p>Meet our diverse team of researchers, students, and alumni</p>
        </header>

        <MemberSection title="Faculty Members" members={faculty.docs} />
        <MemberSection title="Postdoc & PhD Students" members={postdocs.docs} />
        <MemberSection title="Graduate Students" members={masters.docs} />
        <MemberSection title="Undergraduate Students" members={undergrads.docs} />
        <MemberSection title="Alumni" members={alumni.docs} />
      </div>
    </div>
  )
}
