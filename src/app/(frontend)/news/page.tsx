import { getPayload } from 'payload'
import React from 'react'
import config from '@/payload.config'
import type { News } from '@/payload-types'

interface NewsPageProps {
  searchParams: Promise<{ category?: string; page?: string }>
}

export default async function NewsPage({ searchParams }: NewsPageProps) {
  const { category, page = '1' } = await searchParams
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  const currentPage = parseInt(page)
  const limit = 12

  // Build query conditions
  const whereConditions: any = {
    isPublished: { equals: true },
  }

  if (category) {
    whereConditions.category = { equals: category }
  }

  const news = await payload.find({
    collection: 'news',
    where: whereConditions,
    sort: '-publishDate',
    limit,
    page: currentPage,
  })

  // Get pinned news for the first page
  const pinnedNews = currentPage === 1 ? await payload.find({
    collection: 'news',
    where: {
      isPublished: { equals: true },
      isPinned: { equals: true },
    },
    sort: '-publishDate',
    limit: 3,
  }) : { docs: [] }

  const categories = [
    { value: '', label: 'All News' },
    { value: 'academic', label: 'Academic Activity' },
    { value: 'award', label: 'Award Information' },
    { value: 'recruitment', label: 'Recruitment' },
    { value: 'event', label: 'Event' },
    { value: 'general', label: 'General' },
  ]

  const NewsCard = ({ newsItem }: { newsItem: News }) => (
    <article className="news-card">
      {newsItem.featuredImage && (
        <div className="news-image">
          <img 
            src={typeof newsItem.featuredImage === 'object' ? newsItem.featuredImage.url || '' : ''} 
            alt={newsItem.title}
          />
        </div>
      )}
      <div className="news-content">
        <div className="news-meta">
          <span className={`category category-${newsItem.category}`}>
            {categories.find(c => c.value === newsItem.category)?.label || newsItem.category}
          </span>
          <time dateTime={newsItem.publishDate}>
            {new Date(newsItem.publishDate).toLocaleDateString()}
          </time>
        </div>
        <h3>
          <a href={`/news/${newsItem.slug}`}>{newsItem.title}</a>
        </h3>
        <p className="excerpt">{newsItem.excerpt}</p>
        
        {newsItem.eventDate && (
          <div className="event-info">
            <strong>Event Date:</strong> {new Date(newsItem.eventDate).toLocaleDateString()}
            {newsItem.eventTime && <span> at {newsItem.eventTime}</span>}
            {newsItem.eventLocation && <span> - {newsItem.eventLocation}</span>}
          </div>
        )}

        <div className="news-actions">
          <a href={`/news/${newsItem.slug}`} className="read-more">
            Read More
          </a>
        </div>
      </div>
    </article>
  )

  const Pagination = () => {
    if (news.totalPages <= 1) return null

    const pages = []
    for (let i = 1; i <= news.totalPages; i++) {
      pages.push(i)
    }

    return (
      <nav className="pagination">
        {news.hasPrevPage && (
          <a href={`/news?page=${currentPage - 1}${category ? `&category=${category}` : ''}`}>
            Previous
          </a>
        )}
        
        {pages.map(pageNum => (
          <a
            key={pageNum}
            href={`/news?page=${pageNum}${category ? `&category=${category}` : ''}`}
            className={pageNum === currentPage ? 'active' : ''}
          >
            {pageNum}
          </a>
        ))}

        {news.hasNextPage && (
          <a href={`/news?page=${currentPage + 1}${category ? `&category=${category}` : ''}`}>
            Next
          </a>
        )}
      </nav>
    )
  }

  return (
    <div className="news-page">
      <div className="container">
        <header className="page-header">
          <h1>News & Events</h1>
          <p>Stay updated with the latest news, events, and announcements from our lab</p>
        </header>

        {/* Category Filter */}
        <div className="category-filter">
          {categories.map(cat => (
            <a
              key={cat.value}
              href={`/news${cat.value ? `?category=${cat.value}` : ''}`}
              className={category === cat.value || (!category && cat.value === '') ? 'active' : ''}
            >
              {cat.label}
            </a>
          ))}
        </div>

        {/* Pinned News */}
        {pinnedNews.docs.length > 0 && (
          <section className="pinned-news">
            <h2>Pinned News</h2>
            <div className="news-grid">
              {pinnedNews.docs.map((newsItem: News) => (
                <NewsCard key={newsItem.id} newsItem={newsItem} />
              ))}
            </div>
          </section>
        )}

        {/* Regular News */}
        <section className="regular-news">
          {pinnedNews.docs.length > 0 && <h2>Latest News</h2>}
          <div className="news-grid">
            {news.docs.map((newsItem: News) => (
              <NewsCard key={newsItem.id} newsItem={newsItem} />
            ))}
          </div>
        </section>

        <Pagination />
      </div>
    </div>
  )
}
