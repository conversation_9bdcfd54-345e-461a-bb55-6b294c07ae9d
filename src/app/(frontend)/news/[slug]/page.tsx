import { getPayload } from 'payload'
import { notFound } from 'next/navigation'
import React from 'react'
import config from '@/payload.config'
import type { News } from '@/payload-types'

interface NewsDetailPageProps {
  params: Promise<{ slug: string }>
}

export default async function NewsDetailPage({ params }: NewsDetailPageProps) {
  const { slug } = await params
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  const newsItems = await payload.find({
    collection: 'news',
    where: {
      slug: { equals: slug },
      isPublished: { equals: true },
    },
    limit: 1,
  })

  if (newsItems.docs.length === 0) {
    notFound()
  }

  const newsItem = newsItems.docs[0] as News

  // Get related news
  const relatedNews = await payload.find({
    collection: 'news',
    where: {
      id: { not_equals: newsItem.id },
      category: { equals: newsItem.category },
      isPublished: { equals: true },
    },
    sort: '-publishDate',
    limit: 3,
  })

  const categories = {
    academic: 'Academic Activity',
    award: 'Award Information',
    recruitment: 'Recruitment',
    event: 'Event',
    general: 'General',
  }

  return (
    <div className="news-detail">
      <div className="container">
        <article className="news-article">
          {/* Breadcrumb */}
          <nav className="breadcrumb">
            <a href="/news">News</a>
            <span>/</span>
            <span>{categories[newsItem.category as keyof typeof categories] || newsItem.category}</span>
          </nav>

          {/* Article Header */}
          <header className="article-header">
            <div className="article-meta">
              <span className={`category category-${newsItem.category}`}>
                {categories[newsItem.category as keyof typeof categories] || newsItem.category}
              </span>
              <time dateTime={newsItem.publishDate}>
                {new Date(newsItem.publishDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </time>
            </div>
            <h1>{newsItem.title}</h1>
            <p className="excerpt">{newsItem.excerpt}</p>
          </header>

          {/* Featured Image */}
          {newsItem.featuredImage && (
            <div className="featured-image">
              <img 
                src={typeof newsItem.featuredImage === 'object' ? newsItem.featuredImage.url || '' : ''} 
                alt={newsItem.title}
              />
            </div>
          )}

          {/* Event Information */}
          {newsItem.eventDate && (
            <div className="event-info-box">
              <h3>Event Information</h3>
              <div className="event-details">
                <div className="event-detail">
                  <strong>Date:</strong> {new Date(newsItem.eventDate).toLocaleDateString()}
                </div>
                {newsItem.eventTime && (
                  <div className="event-detail">
                    <strong>Time:</strong> {newsItem.eventTime}
                  </div>
                )}
                {newsItem.eventLocation && (
                  <div className="event-detail">
                    <strong>Location:</strong> {newsItem.eventLocation}
                  </div>
                )}
                {newsItem.eventRegistrationLink && (
                  <div className="event-detail">
                    <a 
                      href={newsItem.eventRegistrationLink} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="registration-link"
                    >
                      Register for Event
                    </a>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Article Content */}
          <div className="article-content">
            <div dangerouslySetInnerHTML={{ __html: newsItem.content }} />
          </div>

          {/* Gallery */}
          {newsItem.gallery && newsItem.gallery.length > 0 && (
            <div className="article-gallery">
              <h3>Gallery</h3>
              <div className="gallery-grid">
                {newsItem.gallery.map((item, index) => (
                  <div key={index} className="gallery-item">
                    <img 
                      src={typeof item.image === 'object' ? item.image.url || '' : ''} 
                      alt={`Gallery image ${index + 1}`}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Downloadable Files */}
          {newsItem.downloadableFiles && newsItem.downloadableFiles.length > 0 && (
            <div className="downloadable-files">
              <h3>Downloads</h3>
              <div className="files-list">
                {newsItem.downloadableFiles.map((item, index) => (
                  <a 
                    key={index}
                    href={typeof item.file === 'object' ? item.file.url || '' : ''}
                    download
                    className="download-link"
                  >
                    Download File {index + 1}
                  </a>
                ))}
              </div>
            </div>
          )}

          {/* Video Embed */}
          {newsItem.videoEmbed && (
            <div className="video-embed">
              <div dangerouslySetInnerHTML={{ __html: newsItem.videoEmbed }} />
            </div>
          )}

          {/* Tags */}
          {newsItem.tags && newsItem.tags.length > 0 && (
            <div className="article-tags">
              <strong>Tags:</strong>
              <div className="tags-list">
                {newsItem.tags.map((tag, index) => (
                  <span key={index} className="tag">
                    {typeof tag === 'object' ? tag.tag : tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* External Link */}
          {newsItem.externalLink && (
            <div className="external-link">
              <a href={newsItem.externalLink} target="_blank" rel="noopener noreferrer">
                Read more at external source
              </a>
            </div>
          )}
        </article>

        {/* Related News */}
        {relatedNews.docs.length > 0 && (
          <aside className="related-news">
            <h2>Related News</h2>
            <div className="related-news-grid">
              {relatedNews.docs.map((related: News) => (
                <article key={related.id} className="related-news-card">
                  {related.featuredImage && (
                    <img 
                      src={typeof related.featuredImage === 'object' ? related.featuredImage.url || '' : ''} 
                      alt={related.title}
                    />
                  )}
                  <div className="related-content">
                    <h3>
                      <a href={`/news/${related.slug}`}>{related.title}</a>
                    </h3>
                    <time dateTime={related.publishDate}>
                      {new Date(related.publishDate).toLocaleDateString()}
                    </time>
                  </div>
                </article>
              ))}
            </div>
          </aside>
        )}
      </div>
    </div>
  )
}
