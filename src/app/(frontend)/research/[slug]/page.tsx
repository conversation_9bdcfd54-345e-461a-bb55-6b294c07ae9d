import { getPayload } from 'payload'
import { notFound } from 'next/navigation'
import React from 'react'
import config from '@/payload.config'
import type { ResearchArea, Project, Publication, Member } from '@/payload-types'

interface ResearchAreaPageProps {
  params: Promise<{ slug: string }>
}

export default async function ResearchAreaPage({ params }: ResearchAreaPageProps) {
  const { slug } = await params
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  const researchAreas = await payload.find({
    collection: 'research-areas',
    where: {
      slug: {
        equals: slug,
      },
      isActive: {
        equals: true,
      },
    },
    limit: 1,
  })

  if (researchAreas.docs.length === 0) {
    notFound()
  }

  const area = researchAreas.docs[0] as ResearchArea

  // Fetch related content
  const [relatedProjects, relatedPublications, relatedMembers] = await Promise.all([
    area.relatedProjects && area.relatedProjects.length > 0
      ? payload.find({
          collection: 'projects',
          where: {
            id: {
              in: area.relatedProjects.map(p => typeof p === 'object' ? p.id : p),
            },
            isPublic: {
              equals: true,
            },
          },
          limit: 6,
        })
      : { docs: [] },
    area.relatedPublications && area.relatedPublications.length > 0
      ? payload.find({
          collection: 'publications',
          where: {
            id: {
              in: area.relatedPublications.map(p => typeof p === 'object' ? p.id : p),
            },
            isPublic: {
              equals: true,
            },
          },
          limit: 10,
        })
      : { docs: [] },
    area.relatedMembers && area.relatedMembers.length > 0
      ? payload.find({
          collection: 'members',
          where: {
            id: {
              in: area.relatedMembers.map(m => typeof m === 'object' ? m.id : m),
            },
            showOnWebsite: {
              equals: true,
            },
            isActive: {
              equals: true,
            },
          },
        })
      : { docs: [] },
  ])

  return (
    <div className="research-area-detail">
      <div className="container">
        {/* Header Section */}
        <header className="area-header">
          {area.coverImage && (
            <div className="cover-image">
              <img 
                src={typeof area.coverImage === 'object' ? area.coverImage.url || '' : ''} 
                alt={area.name}
              />
            </div>
          )}
          <div className="header-content">
            <h1>{area.name}</h1>
            <div className="description" dangerouslySetInnerHTML={{ __html: area.description }} />
          </div>
        </header>

        {/* Research Goals */}
        {area.researchGoals && (
          <section className="research-goals">
            <h2>Research Goals</h2>
            <div dangerouslySetInnerHTML={{ __html: area.researchGoals }} />
          </section>
        )}

        {/* Key Technologies */}
        {area.keyTechnologies && area.keyTechnologies.length > 0 && (
          <section className="key-technologies">
            <h2>Key Technologies</h2>
            <div className="tech-tags">
              {area.keyTechnologies.map((tech, index) => (
                <span key={index} className="tech-tag">
                  {typeof tech === 'object' ? tech.technology : tech}
                </span>
              ))}
            </div>
          </section>
        )}

        {/* Related Projects */}
        {relatedProjects.docs.length > 0 && (
          <section className="related-projects">
            <h2>Related Projects</h2>
            <div className="projects-grid">
              {relatedProjects.docs.map((project: Project) => (
                <div key={project.id} className="project-card">
                  {project.coverImage && (
                    <img 
                      src={typeof project.coverImage === 'object' ? project.coverImage.url || '' : ''} 
                      alt={project.title}
                    />
                  )}
                  <h3>{project.title}</h3>
                  <p>{project.shortDescription}</p>
                  <span className={`status status-${project.status}`}>
                    {project.status}
                  </span>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Related Publications */}
        {relatedPublications.docs.length > 0 && (
          <section className="related-publications">
            <h2>Related Publications</h2>
            <div className="publications-list">
              {relatedPublications.docs.map((pub: Publication) => (
                <div key={pub.id} className="publication-item">
                  <h4>{pub.title}</h4>
                  <p className="authors">{pub.authors}</p>
                  <p className="venue">{pub.venue}, {pub.year}</p>
                  {pub.doi && (
                    <a href={`https://doi.org/${pub.doi}`} target="_blank" rel="noopener noreferrer">
                      DOI: {pub.doi}
                    </a>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Related Members */}
        {relatedMembers.docs.length > 0 && (
          <section className="related-members">
            <h2>Team Members</h2>
            <div className="members-grid">
              {relatedMembers.docs.map((member: Member) => (
                <div key={member.id} className="member-card">
                  {member.photo && (
                    <img 
                      src={typeof member.photo === 'object' ? member.photo.url || '' : ''} 
                      alt={member.name}
                    />
                  )}
                  <h4>{member.name}</h4>
                  <p className="position">{member.position}</p>
                  <p className="role">{member.role}</p>
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  )
}
