import { getPayload } from 'payload'
import React from 'react'
import config from '@/payload.config'
import type { ResearchArea } from '@/payload-types'

export default async function ResearchPage() {
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  const researchAreas = await payload.find({
    collection: 'research-areas',
    where: {
      isActive: {
        equals: true,
      },
    },
    sort: 'order',
  })

  return (
    <div className="research-page">
      <div className="container">
        <header className="page-header">
          <h1>Research Areas</h1>
          <p>Explore our diverse research domains in brain-computer interfaces and machine intelligence</p>
        </header>

        <div className="research-areas-grid">
          {researchAreas.docs.map((area: ResearchArea) => (
            <div key={area.id} className="research-area-card">
              {area.icon && (
                <div className="area-icon">
                  <img 
                    src={typeof area.icon === 'object' ? area.icon.url || '' : ''} 
                    alt={area.name}
                  />
                </div>
              )}
              <h2>{area.name}</h2>
              <p>{area.shortDescription}</p>
              <div className="area-actions">
                <a href={`/research/${area.slug}`} className="btn-primary">
                  Learn More
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
