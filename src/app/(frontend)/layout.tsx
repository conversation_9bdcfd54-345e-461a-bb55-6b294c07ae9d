import config from '@/payload.config'
import { getPayload } from 'payload'
import React from 'react'
import './styles.css'

export async function generateMetadata() {
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  const homepageSettings = (await payload.findGlobal({
    slug: 'homepage-settings',
  })) as HomepageSetting

  return {
    title: homepageSettings.siteTitle || 'BCMI Lab',
    description:
      homepageSettings.siteDescription || 'Brain-Computer and Machine Intelligence Laboratory',
    icons: {
      icon:
        homepageSettings.siteFavicon && typeof homepageSettings.siteFavicon === 'object'
          ? homepageSettings.siteFavicon.url
          : '/favicon.ico',
    },
  }
}

export default async function RootLayout(props: { children: React.ReactNode }) {
  const { children } = props
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  const homepageSettings = (await payload.findGlobal({
    slug: 'homepage-settings',
  })) as HomepageSetting

  return (
    <html lang="en">
      <body>
        <header className="site-header">
          <nav className="main-nav">
            <div className="container">
              <div className="nav-brand">
                {homepageSettings.siteLogo && (
                  <img
                    src={
                      typeof homepageSettings.siteLogo === 'object'
                        ? homepageSettings.siteLogo.url || ''
                        : ''
                    }
                    alt={homepageSettings.siteTitle || 'BCMI Lab'}
                    className="site-logo"
                  />
                )}
                <span className="site-title">{homepageSettings.siteTitle || 'BCMI Lab'}</span>
              </div>
              <ul className="nav-menu">
                <li>
                  <a href="/">Home</a>
                </li>
                <li>
                  <a href="/research">Research</a>
                </li>
                <li>
                  <a href="/members">Members</a>
                </li>
                <li>
                  <a href="/news">News</a>
                </li>
                <li>
                  <a href="/resources">Resources</a>
                </li>
              </ul>
            </div>
          </nav>
        </header>
        <main>{children}</main>
        <footer className="site-footer">
          <div className="container">
            <div className="footer-content">
              <div className="footer-section">
                <h3>{homepageSettings.siteTitle || 'BCMI Lab'}</h3>
                <p>{homepageSettings.siteDescription}</p>
              </div>
              <div className="footer-section">
                <h4>Quick Links</h4>
                <ul>
                  <li>
                    <a href="/research">Research Areas</a>
                  </li>
                  <li>
                    <a href="/members">Team Members</a>
                  </li>
                  <li>
                    <a href="/news">Latest News</a>
                  </li>
                  <li>
                    <a href="/resources">Resources</a>
                  </li>
                </ul>
              </div>
              {homepageSettings.contactEnabled && (
                <div className="footer-section">
                  <h4>Contact</h4>
                  {homepageSettings.contactEmail && (
                    <p>
                      Email:{' '}
                      <a href={`mailto:${homepageSettings.contactEmail}`}>
                        {homepageSettings.contactEmail}
                      </a>
                    </p>
                  )}
                  {homepageSettings.contactPhone && <p>Phone: {homepageSettings.contactPhone}</p>}
                </div>
              )}
            </div>
            <div className="footer-bottom">
              <p>
                &copy; {new Date().getFullYear()} {homepageSettings.siteTitle || 'BCMI Lab'}. All
                rights reserved.
              </p>
            </div>
          </div>
        </footer>
      </body>
    </html>
  )
}
