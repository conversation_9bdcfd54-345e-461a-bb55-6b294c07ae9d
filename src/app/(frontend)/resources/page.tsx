import { getPayload } from 'payload'
import React from 'react'
import config from '@/payload.config'
import type { Resource } from '@/payload-types'

interface ResourcesPageProps {
  searchParams: Promise<{ category?: string }>
}

export default async function ResourcesPage({ searchParams }: ResourcesPageProps) {
  const { category } = await searchParams
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  // Build query conditions
  const whereConditions: any = {
    isActive: { equals: true },
    accessLevel: { in: ['public', 'members-only'] }, // Exclude restricted resources
  }

  if (category) {
    whereConditions.category = { equals: category }
  }

  const resources = await payload.find({
    collection: 'resources',
    where: whereConditions,
    sort: '-lastUpdated',
  })

  const categories = [
    { value: '', label: 'All Resources' },
    { value: 'dataset', label: 'Datasets' },
    { value: 'software', label: 'Software' },
    { value: 'documentation', label: 'Documentation' },
    { value: 'teaching-material', label: 'Teaching Materials' },
    { value: 'tool', label: 'Tools' },
    { value: 'other', label: 'Other' },
  ]

  const ResourceCard = ({ resource }: { resource: Resource }) => (
    <div className="resource-card">
      <div className="resource-header">
        <h3>{resource.title}</h3>
        <div className="resource-meta">
          <span className={`category category-${resource.category}`}>
            {categories.find(c => c.value === resource.category)?.label || resource.category}
          </span>
          <span className={`access-level access-${resource.accessLevel}`}>
            {resource.accessLevel === 'public' ? 'Public' : 'Members Only'}
          </span>
        </div>
      </div>

      <div className="resource-content">
        <p className="description">{resource.shortDescription}</p>

        {resource.tags && resource.tags.length > 0 && (
          <div className="resource-tags">
            {resource.tags.map((tag, index) => (
              <span key={index} className="tag">
                {typeof tag === 'object' ? tag.tag : tag}
              </span>
            ))}
          </div>
        )}

        <div className="resource-details">
          {resource.version && (
            <div className="detail">
              <strong>Version:</strong> {resource.version}
            </div>
          )}
          {resource.fileSize && (
            <div className="detail">
              <strong>Size:</strong> {resource.fileSize}
            </div>
          )}
          {resource.fileFormat && (
            <div className="detail">
              <strong>Format:</strong> {resource.fileFormat}
            </div>
          )}
          {resource.licenseType && (
            <div className="detail">
              <strong>License:</strong> {resource.licenseType}
            </div>
          )}
          <div className="detail">
            <strong>Last Updated:</strong> {new Date(resource.lastUpdated).toLocaleDateString()}
          </div>
          {resource.downloadCount !== undefined && (
            <div className="detail">
              <strong>Downloads:</strong> {resource.downloadCount}
            </div>
          )}
        </div>

        <div className="resource-actions">
          {resource.resourceFile && (
            <a 
              href={typeof resource.resourceFile === 'object' ? resource.resourceFile.url || '' : ''}
              download
              className="btn-primary"
            >
              Download
            </a>
          )}
          {resource.externalLink && (
            <a 
              href={resource.externalLink}
              target="_blank"
              rel="noopener noreferrer"
              className="btn-secondary"
            >
              External Link
            </a>
          )}
          {resource.githubRepo && (
            <a 
              href={resource.githubRepo}
              target="_blank"
              rel="noopener noreferrer"
              className="btn-secondary"
            >
              GitHub
            </a>
          )}
        </div>

        {resource.systemRequirements && (
          <div className="system-requirements">
            <strong>System Requirements:</strong> {resource.systemRequirements}
          </div>
        )}

        {resource.requiresRegistration && (
          <div className="registration-notice">
            <em>Registration required for download</em>
          </div>
        )}
      </div>
    </div>
  )

  // Group resources by category for better organization
  const groupedResources = resources.docs.reduce((acc, resource) => {
    const cat = resource.category
    if (!acc[cat]) {
      acc[cat] = []
    }
    acc[cat].push(resource)
    return acc
  }, {} as Record<string, Resource[]>)

  return (
    <div className="resources-page">
      <div className="container">
        <header className="page-header">
          <h1>Resources</h1>
          <p>Access datasets, software tools, documentation, and teaching materials from our research</p>
        </header>

        {/* Category Filter */}
        <div className="category-filter">
          {categories.map(cat => (
            <a
              key={cat.value}
              href={`/resources${cat.value ? `?category=${cat.value}` : ''}`}
              className={category === cat.value || (!category && cat.value === '') ? 'active' : ''}
            >
              {cat.label}
            </a>
          ))}
        </div>

        {/* Resources Display */}
        {category ? (
          // Show filtered resources
          <div className="resources-grid">
            {resources.docs.map((resource: Resource) => (
              <ResourceCard key={resource.id} resource={resource} />
            ))}
          </div>
        ) : (
          // Show grouped resources
          <div className="resources-sections">
            {Object.entries(groupedResources).map(([categoryKey, categoryResources]) => {
              const categoryLabel = categories.find(c => c.value === categoryKey)?.label || categoryKey
              return (
                <section key={categoryKey} className="resource-category-section">
                  <h2>{categoryLabel}</h2>
                  <div className="resources-grid">
                    {categoryResources.map((resource: Resource) => (
                      <ResourceCard key={resource.id} resource={resource} />
                    ))}
                  </div>
                </section>
              )
            })}
          </div>
        )}

        {resources.docs.length === 0 && (
          <div className="no-resources">
            <p>No resources found for the selected category.</p>
          </div>
        )}
      </div>
    </div>
  )
}
