import type { News, Project, ResearchArea } from '@/payload-types'
import config from '@/payload.config'
import { getPayload } from 'payload'
import './styles.css'

export default async function HomePage() {
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })

  // Fetch homepage settings
  const homepageSettings = (await payload.findGlobal({
    slug: 'homepage-settings',
  })) as HomepageSettings

  // Fetch featured content
  const [featuredNews, featuredResearchAreas, featuredProjects] = await Promise.all([
    homepageSettings.featuredNews && homepageSettings.featuredNews.length > 0
      ? payload.find({
          collection: 'news',
          where: {
            id: {
              in: homepageSettings.featuredNews.map((n) => (typeof n === 'object' ? n.id : n)),
            },
            isPublished: { equals: true },
          },
          sort: '-publishDate',
          limit: 6,
        })
      : { docs: [] },
    homepageSettings.featuredResearchAreas && homepageSettings.featuredResearchAreas.length > 0
      ? payload.find({
          collection: 'research-areas',
          where: {
            id: {
              in: homepageSettings.featuredResearchAreas.map((r) =>
                typeof r === 'object' ? r.id : r,
              ),
            },
            isActive: { equals: true },
          },
          sort: 'order',
        })
      : { docs: [] },
    homepageSettings.featuredProjectsEnabled &&
    homepageSettings.featuredProjects &&
    homepageSettings.featuredProjects.length > 0
      ? payload.find({
          collection: 'projects',
          where: {
            id: {
              in: homepageSettings.featuredProjects.map((p) => (typeof p === 'object' ? p.id : p)),
            },
            isPublic: { equals: true },
          },
          sort: 'displayOrder',
        })
      : { docs: [] },
  ])

  return (
    <div className="homepage">
      {/* Lab Introduction Banner */}
      <section className="hero-banner">
        {homepageSettings.labIntroBackgroundImage && (
          <div className="hero-background">
            <img
              src={
                typeof homepageSettings.labIntroBackgroundImage === 'object'
                  ? homepageSettings.labIntroBackgroundImage.url || ''
                  : ''
              }
              alt="Lab Background"
            />
          </div>
        )}
        <div className="hero-content">
          <div className="container">
            <h1>{homepageSettings.labIntroTitle}</h1>
            {homepageSettings.labIntroSubtitle && <h2>{homepageSettings.labIntroSubtitle}</h2>}
            <div className="hero-description">
              <div dangerouslySetInnerHTML={{ __html: homepageSettings.labIntroDescription }} />
            </div>
          </div>
        </div>
      </section>

      {/* Featured News Carousel */}
      {featuredNews.docs.length > 0 && (
        <section className="featured-news">
          <div className="container">
            <h2>Latest News</h2>
            <div className="news-carousel">
              {featuredNews.docs.map((newsItem: News) => (
                <article key={newsItem.id} className="news-carousel-item">
                  {newsItem.featuredImage && (
                    <div className="news-image">
                      <img
                        src={
                          typeof newsItem.featuredImage === 'object'
                            ? newsItem.featuredImage.url || ''
                            : ''
                        }
                        alt={newsItem.title}
                      />
                    </div>
                  )}
                  <div className="news-content">
                    <h3>
                      <a href={`/news/${newsItem.slug}`}>{newsItem.title}</a>
                    </h3>
                    <p>{newsItem.excerpt}</p>
                    <time dateTime={newsItem.publishDate}>
                      {new Date(newsItem.publishDate).toLocaleDateString()}
                    </time>
                  </div>
                </article>
              ))}
            </div>
            <div className="section-actions">
              <a href="/news" className="btn-primary">
                View All News
              </a>
            </div>
          </div>
        </section>
      )}

      {/* Research Areas Quick Navigation */}
      {featuredResearchAreas.docs.length > 0 && (
        <section className="research-areas-preview">
          <div className="container">
            <h2>Research Areas</h2>
            <div className="research-areas-grid">
              {featuredResearchAreas.docs.map((area: ResearchArea) => (
                <div key={area.id} className="research-area-card">
                  {area.icon && (
                    <div className="area-icon">
                      <img
                        src={typeof area.icon === 'object' ? area.icon.url || '' : ''}
                        alt={area.name}
                      />
                    </div>
                  )}
                  <h3>{area.name}</h3>
                  <p>{area.shortDescription}</p>
                  <a href={`/research/${area.slug}`} className="btn-secondary">
                    Learn More
                  </a>
                </div>
              ))}
            </div>
            <div className="section-actions">
              <a href="/research" className="btn-primary">
                Explore All Research
              </a>
            </div>
          </div>
        </section>
      )}

      {/* Featured Projects */}
      {homepageSettings.featuredProjectsEnabled && featuredProjects.docs.length > 0 && (
        <section className="featured-projects">
          <div className="container">
            <h2>Featured Projects</h2>
            <div className="projects-grid">
              {featuredProjects.docs.map((project: Project) => (
                <div key={project.id} className="project-card">
                  {project.coverImage && (
                    <div className="project-image">
                      <img
                        src={
                          typeof project.coverImage === 'object' ? project.coverImage.url || '' : ''
                        }
                        alt={project.title}
                      />
                    </div>
                  )}
                  <div className="project-content">
                    <h3>{project.title}</h3>
                    <p>{project.shortDescription}</p>
                    <span className={`status status-${project.status}`}>{project.status}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Contact Information */}
      {homepageSettings.contactEnabled && (
        <section className="contact-info">
          <div className="container">
            <h2>{homepageSettings.contactTitle || 'Contact Us'}</h2>
            <div className="contact-content">
              <div className="contact-details">
                {homepageSettings.contactAddress && (
                  <div className="contact-item">
                    <strong>Address:</strong>
                    <p>{homepageSettings.contactAddress}</p>
                  </div>
                )}
                {homepageSettings.contactPhone && (
                  <div className="contact-item">
                    <strong>Phone:</strong>
                    <p>{homepageSettings.contactPhone}</p>
                  </div>
                )}
                {homepageSettings.contactEmail && (
                  <div className="contact-item">
                    <strong>Email:</strong>
                    <p>
                      <a href={`mailto:${homepageSettings.contactEmail}`}>
                        {homepageSettings.contactEmail}
                      </a>
                    </p>
                  </div>
                )}
              </div>
              {homepageSettings.contactMapEmbed && (
                <div className="contact-map">
                  <div dangerouslySetInnerHTML={{ __html: homepageSettings.contactMapEmbed }} />
                </div>
              )}
            </div>
          </div>
        </section>
      )}
    </div>
  )
}
